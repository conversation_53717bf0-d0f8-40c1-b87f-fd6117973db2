<?php
// مكون الإشعارات - يتم تضمينه في جميع الصفحات
if(!isset($_SESSION['user'])) {
    return;
}

include_once "notifications.php";
echo getNotificationCSS();
?>

<!-- مكون الإشعارات -->
<div class="notification-container" id="notificationContainer">
    <div class="notification-bell" onclick="toggleNotifications()">
        <i class="fas fa-bell" style="color: white; font-size: 18px;"></i>
        <span id="notificationBadge" class="notification-badge" style="display: none;">0</span>
    </div>
    
    <div class="notification-dropdown" id="notificationDropdown">
        <div style="padding: 15px; border-bottom: 2px solid #f0f0f0; background: linear-gradient(45deg, #667eea, #764ba2); color: white;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <h6 style="margin: 0; font-weight: bold;">الإشعارات</h6>
                <button onclick="markAllAsRead()" style="background: none; border: none; color: white; cursor: pointer; font-size: 12px;">
                    <i class="fas fa-check-double"></i> تمييز الكل كمقروء
                </button>
            </div>
        </div>
        <div id="notificationsList">
            <div style="padding: 20px; text-align: center; color: #6c757d;">
                <i class="fas fa-spinner fa-spin"></i> جاري التحميل...
            </div>
        </div>
    </div>
</div>

<script>
let notificationDropdownOpen = false;

// تحديث عدادات الإشعارات
function updateNotificationCounts() {
    fetch('<?php echo ($_SESSION['user']->role == 'User') ? '../ajax/notifications.php' : 'ajax/notifications.php'; ?>?action=get_counts')
        .then(response => response.json())
        .then(data => {
            const badge = document.getElementById('notificationBadge');
            if (data.unread_notifications > 0) {
                badge.textContent = data.unread_notifications > 99 ? '99+' : data.unread_notifications;
                badge.style.display = 'flex';
            } else {
                badge.style.display = 'none';
            }
        })
        .catch(error => console.error('خطأ في تحديث الإشعارات:', error));
}

// تحديث شارات الصفحات الفرعية
function updatePageBadges() {
    <?php if($_SESSION['user']->role == 'Admin' || $_SESSION['user']->role == 'Mod'): ?>
    // للإدارة والمدققين - تحديث شارات الاحتياجات والإجازات
    fetch('<?php echo ($_SESSION['user']->role == 'User') ? '../ajax/notifications.php' : 'ajax/notifications.php'; ?>?action=get_admin_counts')
        .then(response => response.json())
        .then(data => {
            // تحديث شارة الاحتياجات
            updateBadgeForElement('manage_needs_link', data.needs_count);
            updateBadgeForElement('needs_button', data.needs_count);
            
            // تحديث شارة الإجازات
            updateBadgeForElement('manage_leaves_link', data.leaves_count);
            updateBadgeForElement('leaves_button', data.leaves_count);
        })
        .catch(error => console.error('خطأ في تحديث شارات الإدارة:', error));
    <?php else: ?>
    // للمستخدمين - تحديث شارات الردود
    fetch('<?php echo ($_SESSION['user']->role == 'User') ? '../ajax/notifications.php' : 'ajax/notifications.php'; ?>?action=get_user_responses')
        .then(response => response.json())
        .then(data => {
            // تحديث شارة احتياجاتي
            updateBadgeForElement('my_needs_link', data.needs_responses);
            updateBadgeForElement('my_needs_button', data.needs_responses);
            
            // تحديث شارة إجازاتي
            updateBadgeForElement('my_leaves_link', data.leaves_responses);
            updateBadgeForElement('my_leaves_button', data.leaves_responses);
        })
        .catch(error => console.error('خطأ في تحديث شارات المستخدم:', error));
    <?php endif; ?>
}

// دالة مساعدة لتحديث الشارة
function updateBadgeForElement(elementId, count) {
    const element = document.getElementById(elementId);
    if (element) {
        let badge = element.querySelector('.notification-badge');
        if (count > 0) {
            if (!badge) {
                badge = document.createElement('span');
                badge.className = 'notification-badge';
                badge.style.position = 'absolute';
                badge.style.top = '-8px';
                badge.style.right = '-8px';
                element.style.position = 'relative';
                element.appendChild(badge);
            }
            badge.textContent = count > 99 ? '99+' : count;
            badge.style.display = 'flex';
        } else if (badge) {
            badge.style.display = 'none';
        }
    }
}

// عرض/إخفاء قائمة الإشعارات
function toggleNotifications() {
    const dropdown = document.getElementById('notificationDropdown');
    notificationDropdownOpen = !notificationDropdownOpen;
    
    if (notificationDropdownOpen) {
        dropdown.style.display = 'block';
        loadNotifications();
    } else {
        dropdown.style.display = 'none';
    }
}

// تحميل الإشعارات
function loadNotifications() {
    fetch('<?php echo ($_SESSION['user']->role == 'User') ? '../ajax/notifications.php' : 'ajax/notifications.php'; ?>?action=get_notifications')
        .then(response => response.json())
        .then(data => {
            const list = document.getElementById('notificationsList');
            if (data.length === 0) {
                list.innerHTML = `
                    <div style="padding: 20px; text-align: center; color: #6c757d;">
                        <i class="fas fa-bell-slash" style="font-size: 24px; margin-bottom: 10px;"></i>
                        <p>لا توجد إشعارات</p>
                    </div>
                `;
                return;
            }
            
            list.innerHTML = data.map(notification => `
                <div class="notification-item ${notification.is_read ? '' : 'unread'}" 
                     onclick="markAsRead(${notification.id})">
                    <div class="notification-header">
                        <i class="${notification.icon}" style="color: ${notification.color};"></i>
                        <span class="notification-title">${notification.title}</span>
                        <span class="notification-time">${notification.time}</span>
                    </div>
                    <div class="notification-message">${notification.message}</div>
                </div>
            `).join('');
        })
        .catch(error => {
            console.error('خطأ في تحميل الإشعارات:', error);
            document.getElementById('notificationsList').innerHTML = `
                <div style="padding: 20px; text-align: center; color: #dc3545;">
                    <i class="fas fa-exclamation-triangle"></i> خطأ في تحميل الإشعارات
                </div>
            `;
        });
}

// تمييز إشعار كمقروء
function markAsRead(notificationId) {
    const formData = new FormData();
    formData.append('notification_id', notificationId);
    
    fetch('<?php echo ($_SESSION['user']->role == 'User') ? '../ajax/notifications.php' : 'ajax/notifications.php'; ?>?action=mark_read', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateNotificationCounts();
            updatePageBadges();
        }
    })
    .catch(error => console.error('خطأ في تمييز الإشعار:', error));
}

// تمييز جميع الإشعارات كمقروءة
function markAllAsRead() {
    fetch('<?php echo ($_SESSION['user']->role == 'User') ? '../ajax/notifications.php' : 'ajax/notifications.php'; ?>?action=mark_all_read', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateNotificationCounts();
            updatePageBadges();
            loadNotifications();
        }
    })
    .catch(error => console.error('خطأ في تمييز الإشعارات:', error));
}

// إغلاق القائمة عند النقر خارجها
document.addEventListener('click', function(event) {
    const container = document.getElementById('notificationContainer');
    if (!container.contains(event.target) && notificationDropdownOpen) {
        document.getElementById('notificationDropdown').style.display = 'none';
        notificationDropdownOpen = false;
    }
});

// تحديث دوري للإشعارات
setInterval(() => {
    updateNotificationCounts();
    updatePageBadges();
}, 30000); // كل 30 ثانية

// تحديث أولي
document.addEventListener('DOMContentLoaded', function() {
    updateNotificationCounts();
    updatePageBadges();
});
</script>
