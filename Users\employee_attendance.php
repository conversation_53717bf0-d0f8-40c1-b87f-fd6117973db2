<?php
session_start();
if (isset($_SESSION['user'])) {
  if ($_SESSION['user']->role === "User") {
  } else {
    header("location:../login.php", true);
    die("");
    echo "dont work";
  }
} else {
  header("location:../login.php", true);
  die("");
}

?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>حضور الموظفين</title>
  <link rel="stylesheet" href="css/bootstrap.min.css">
  <link rel="stylesheet" href="css/styles.css">
  <link rel="stylesheet" href="css/all.min.css">
  <script src="js/all.min.js"></script>
  <link rel="icon" href="css/icon.ico">
  <script src="js/jquery.min.js"></script>
  <script src="js/jquery.dataTables.min.js"></script>
  <link rel="stylesheet" href="css/jquery.dataTables.min.css">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <?php include "addon/topbar.php" ?>
  <?php include "addon/dbcon.php" ?>
  
  <style>
    /* Modal styles for leave date selection */
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.5);
    }
    
    .modal-content {
      background-color: #fefefe;
      margin: 15% auto;
      padding: 20px;
      border: 1px solid #888;
      border-radius: 10px;
      width: 400px;
      text-align: center;
    }
    
    .close {
      color: #aaa;
      float: right;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
    }
    
    .close:hover {
      color: black;
    }
    
    .date-input {
      margin: 10px 0;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 5px;
      width: 100%;
    }
    
    .modal-buttons {
      margin-top: 20px;
    }
    
    .modal-buttons button {
      margin: 0 5px;
      padding: 8px 20px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }
    
    .btn-confirm {
      background-color: #28a745;
      color: white;
    }
    
    .btn-cancel {
      background-color: #6c757d;
      color: white;
    }

    /* رسالة تحديد المستخدم */
    .no-user-message {
      text-align: center;
      padding: 50px;
      color: #6c757d;
      font-size: 18px;
    }
  </style>
</head>

<body id="td">

<div class="search">
  <button class="btn btn-secondary text-light mx-2" id="show" type="button">عرض حضور الموظفين اليوم</button>
</div>
 
<div class="search d-flex align-items-center mb-3">
  <form action="" method="POST" class="d-flex">
    <button name="show_my_employees" type="submit" class="btn btn-secondary">إظهار موظفيني</button>
  </form>
</div>

  <div class="wrapper2" id="tost_info">
    <div id="toast2">
      <div class="container-11">
        <i id="icon" class="fa-solid fa-circle-info"></i>
      </div>
      <div class="container-22">
        <p class="p1">Done !</p>
        <p class="p2">تم اضافة البينانات</p>
      </div>
    </div>
  </div>

  <!-- Modal for Leave Date Selection -->
  <div id="leaveModal" class="modal">
    <div class="modal-content">
      <span class="close">&times;</span>
      <h3>تحديد فترة الإجازة</h3>
      <div style="margin: 20px 0;">
        <label>تاريخ بداية الإجازة:</label>
        <input type="date" id="leaveStartDate" class="date-input" />
      </div>
      <div style="margin: 20px 0;">
        <label>تاريخ نهاية الإجازة:</label>
        <input type="date" id="leaveEndDate" class="date-input" />
      </div>
      <div class="modal-buttons">
        <button class="btn-confirm" id="confirmLeave">تأكيد الإجازة</button>
        <button class="btn-cancel" id="cancelLeave">إلغاء</button>
      </div>
    </div>
  </div>

  <table class="table" id="Table">
    <thead>
      <tr>
        <th scope="col">إجازة</th>
        <th scope="col">عمليات الغياب</th>
        <th scope="col">عمليات الحضور</th>
        <th scope="col">اسم المستخدم</th>
        <th scope="col">التاريخ</th>
        <th scope="col">اسم الموظف</th>
      </tr>
    </thead>

    <tbody id="myTable">

<?php
$datenow = date('Y-m-d');

// عرض البيانات فقط إذا تم الضغط على زر الإظهار باستخدام المستخدم المسجل دخول حالياً
if (isset($_POST['show_my_employees'])) {
  $current_user_id = $_SESSION['user']->id_user; // جلب ID المستخدم الحالي من الجلسة

  // تعديل الـ SQL query لجلب كل الموظفين للمستخدم الحالي
  $sql = "SELECT employ_tb.id_employ, employ_tb.f_name, employ_tb.b_date, employ_tb.job, employ_tb.date_start, users_tb.user_name, employ_tb.userID
          FROM employ_tb 
          LEFT JOIN users_tb ON employ_tb.userID = users_tb.id_user 
          WHERE employ_tb.userID = '$current_user_id'
          ORDER BY employ_tb.f_name ASC";
  
  $result = mysqli_query($con, $sql);

  if ($result && mysqli_num_rows($result) > 0) {
    while ($row = mysqli_fetch_assoc($result)) {
      $employee_id = $row['id_employ'];
?>

      <tr id="tr_<?php echo $employee_id ?>" class="sm">
        <td>
          <div class="attandes">
            <input type="checkbox" name="leave[]" id="leave_<?php echo $employee_id ?>" value="إجازة">
            <label for="leave_<?php echo $employee_id ?>">إجازة</label>
          </div>
        </td>
        <td>
          <div class="attandes">
            <input type="checkbox" name="absent[]" id="absent_<?php echo $employee_id ?>" value="غائب">
            <label for="absent_<?php echo $employee_id ?>">غائب</label>
          </div>
        </td>
        <td>
          <div class="attandes">
            <input type="checkbox" name="present[]" id="present_<?php echo $employee_id ?>" value="حاضر">
            <label for="present_<?php echo $employee_id ?>">حاضر</label>
          </div>
        </td>
        <td><?php echo $row['user_name'] ? $row['user_name'] : 'غير محدد' ?></td>
        <td><?php echo $datenow ?></td>
        <td><?php echo $row['f_name'] ?></td>
      </tr>

<?php
    }
  } else {
    // إذا لم توجد موظفين للمستخدم الحالي
    echo "<tr><td colspan='6' class='no-user-message'>لا توجد موظفين مرتبطين بحسابك</td></tr>";
  }
} else {
  // رسالة توضيحية قبل الضغط على زر الإظهار
  echo "<tr><td colspan='6' class='no-user-message'>اضغط على 'إظهار موظفيني' لعرض الموظفين المرتبطين بحسابك</td></tr>";
}
?>
    </tbody>
  </table>

<script>
let currentEmployeeId = null;

// تسجيل الحضور
$('input[name="present[]"]').click(function(){
    if($(this).is(':checked')) {
        var x = this.id.replace('present_', '');
        var y = $(this).val();
        console.log('Present clicked - Employee ID:', x, 'Status:', y);
        // إلغاء تحديد الخيارات الأخرى
        $('#absent_' + x + ', #leave_' + x).prop('checked', false);
        sendEmployeeAttendance(x, y);
    }
});

// تسجيل الغياب
$('input[name="absent[]"]').click(function(){
    if($(this).is(':checked')) {
        var x = this.id.replace('absent_', '');
        var y = $(this).val();
        console.log('Absent clicked - Employee ID:', x, 'Status:', y);
        // إلغاء تحديد الخيارات الأخرى
        $('#present_' + x + ', #leave_' + x).prop('checked', false);
        sendEmployeeAttendance(x, y);
    }
});

// تسجيل الإجازة - فتح المودال
$('input[name="leave[]"]').click(function(){
    if($(this).is(':checked')) {
        var x = this.id.replace('leave_', '');
        currentEmployeeId = x;
        // إلغاء تحديد الخيارات الأخرى
        $('#present_' + x + ', #absent_' + x).prop('checked', false);
        // فتح المودال لاختيار التواريخ
        $('#leaveModal').show();
        // تعيين التاريخ الافتراضي
        $('#leaveStartDate').val(getCurrentDate());
        $('#leaveEndDate').val(getCurrentDate());
    } else {
        // إذا تم إلغاء التحديد، إلغاء المتغير
        currentEmployeeId = null;
    }
});

// إغلاق المودال
$('.close, #cancelLeave').click(function() {
    $('#leaveModal').hide();
    if (currentEmployeeId) {
        $('#leave_' + currentEmployeeId).prop('checked', false);
        currentEmployeeId = null;
    }
});

// تأكيد الإجازة
$('#confirmLeave').click(function() {
    var startDate = $('#leaveStartDate').val();
    var endDate = $('#leaveEndDate').val();
    
    if (!startDate || !endDate) {
        alert('يرجى تحديد تاريخ البداية والنهاية');
        return;
    }
    
    if (startDate > endDate) {
        alert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
        return;
    }
    
    // إرسال بيانات الإجازة
    sendEmployeeLeave(currentEmployeeId, startDate, endDate);
    $('#leaveModal').hide();
});

// إغلاق المودال عند النقر خارجها
$(window).click(function(event) {
    if (event.target == $('#leaveModal')[0]) {
        $('#leaveModal').hide();
        if (currentEmployeeId) {
            $('#leave_' + currentEmployeeId).prop('checked', false);
            currentEmployeeId = null;
        }
    }
});

function getCurrentDate() {
    var today = new Date();
    var dd = String(today.getDate()).padStart(2, '0');
    var mm = String(today.getMonth() + 1).padStart(2, '0');
    var yyyy = today.getFullYear();
    return yyyy + '-' + mm + '-' + dd;
}

function sendEmployeeAttendance(x, y) {
    console.log('Sending data - ID:', x, 'Status:', y);
    $.ajax({
        type: "POST",
        url: "addon/employee_code.php",
        data: {
            id: x,
            stat: y
        },
        dataType: "text",
        success: function (data) {
            console.log('Response from server:', data);
            if(data == 1) {
                StudToast('8px solid rgb(247, 167, 22)','#f7a716'," ! انتبه ","   الموظف قد تم تسجيله لهذا اليوم ","fa-solid fa-circle-info");
                jQuery("#tr_"+x).css("background","#ff4e0069");
                jQuery("#tr_"+x).hide(2000);
            } else if(data == "employee_not_found") {
                StudToast('8px solid rgb(220, 53, 69)','rgb(220, 53, 69)'," ! خطأ ","   الموظف غير موجود ","fa-solid fa-circle-exclamation");
            } else if(data == "has_leave") {
                StudToast('8px solid rgb(255, 193, 7)','rgb(255, 193, 7)'," ! تنبيه ","   الموظف في إجازة ولا يمكن تسجيل حضور أو غياب ","fa-solid fa-circle-info");
            } else if(data == "done") {
                if(y === 'حاضر') {
                    StudToast('8px solid rgb(3, 188, 77)','rgb(3, 188, 77)'," ! تمت العملية ","   تم تسجيل الحضور بنجاح ","fa fa-circle-check");
                    jQuery("#tr_"+x).css("background","#54ff0047");
                } else if(y === 'غائب') {
                    StudToast('8px solid rgb(3, 188, 77)','rgb(3, 188, 77)'," ! تمت العملية ","   تم تسجيل الغياب بنجاح ","fa fa-circle-check");
                    jQuery("#tr_"+x).css("background","#dc354547");
                }
                jQuery("#tr_"+x).hide(2000);
            } else {
                StudToast('8px solid rgb(220, 53, 69)','rgb(220, 53, 69)'," ! خطأ ","   " + data + " ","fa-solid fa-circle-exclamation");
            }
        },
        error: function(xhr, status, error) {
            console.log('AJAX Error:', error);
            StudToast('8px solid rgb(220, 53, 69)','rgb(220, 53, 69)'," ! خطأ ","   حدث خطأ في الاتصال ","fa-solid fa-circle-exclamation");
        }
    });
}

function sendEmployeeLeave(employeeId, startDate, endDate) {
    console.log('Sending leave data - ID:', employeeId, 'Start:', startDate, 'End:', endDate);
    $.ajax({
        type: "POST",
        url: "addon/employee_code.php",
        data: {
            id: employeeId,
            stat: 'إجازة',
            leave_start: startDate,
            leave_end: endDate
        },
        dataType: "text",
        success: function (data) {
            console.log('Response from server:', data);
            if(data == 1) {
                StudToast('8px solid rgb(247, 167, 22)','#f7a716'," ! انتبه ","   الموظف قد تم تسجيله لهذا اليوم ","fa-solid fa-circle-info");
                jQuery("#tr_"+employeeId).css("background","#ff4e0069");
                jQuery("#tr_"+employeeId).hide(2000);
            } else if(data == "employee_not_found") {
                StudToast('8px solid rgb(220, 53, 69)','rgb(220, 53, 69)'," ! خطأ ","   الموظف غير موجود ","fa-solid fa-circle-exclamation");
            } else if(data == "done") {
                StudToast('8px solid rgb(3, 188, 77)','rgb(3, 188, 77)'," ! تمت العملية ","   تم تسجيل الإجازة بنجاح من " + startDate + " إلى " + endDate + " ","fa fa-circle-check");
                jQuery("#tr_"+employeeId).css("background","#ffc10747");
                jQuery("#tr_"+employeeId).hide(2000);
            } else {
                StudToast('8px solid rgb(220, 53, 69)','rgb(220, 53, 69)'," ! خطأ ","   " + data + " ","fa-solid fa-circle-exclamation");
            }
            currentEmployeeId = null;
        },
        error: function(xhr, status, error) {
            console.log('AJAX Error:', error);
            StudToast('8px solid rgb(220, 53, 69)','rgb(220, 53, 69)'," ! خطأ ","   حدث خطأ في الاتصال ","fa-solid fa-circle-exclamation");
            currentEmployeeId = null;
        }
    });
}

$("#show").click(() => {
    console.log("clicked");
    location.href = "employee_attendance_view.php";
});
</script>

<script>
$(document).ready(function() {
    // متهيئش DataTables لحد ما المستخدم يضغط على زر الإظهار
    $('button[name="show_my_employees"]').on('click', function() {
        setTimeout(function() {
            // تأكد إن فيه بيانات موظفين حقيقية
            if ($('#myTable tr').length > 0 && !$('.no-user-message').length) {
                $("#Table").DataTable({
                    "language": {
                        "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
                    },
                    "columnDefs": [
                        { "orderable": false, "targets": [0, 1, 2] }
                    ]
                });
            }
        }, 500); // زود الوقت شوية عشان البيانات تحمل كويس
    });
});
</script>

<script>
let x;
let toast = document.getElementById("toast2");
p1 = document.querySelector(".p1");
p2 = document.querySelector(".p2");

function StudToast(ts, ic, tx1, tx2, icC) {
    let icon = document.getElementById("icon");
    clearTimeout(x);
    icon.className = icC;
    toast.style.borderRight = ts;
    icon.style.color = ic;
    p1.innerText = tx1;
    p2.innerText = tx2;
    toast.style.transition = '1s';
    toast.style.transform = "translateX(0)";
    toast.style.transition = '1s';
    x = setTimeout(() => {
        toast.style.transform = "translateX(-500px)";
    }, 4200);
}
</script>

</body>
</html>