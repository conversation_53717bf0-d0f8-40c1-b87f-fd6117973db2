<?php
// اختبار الاتصال بقاعدة البيانات
try {
    $user='kidzrcle_rwda';
    $pass='kidzrcle_rwda';
    $pdo= new PDO ('mysql:host=localhost;dbname=kidzrcle_rwda;charset=utf8',$user,$pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>✅ تم الاتصال بقاعدة البيانات بنجاح</h2>";
    
    // التحقق من وجود جدول المستخدمين
    $checkTable = $pdo->query("SHOW TABLES LIKE 'users_tb'");
    if($checkTable->rowCount() > 0) {
        echo "<h3>✅ جدول المستخدمين موجود</h3>";
        
        // عرض جميع المستخدمين
        $users = $pdo->query("SELECT id_user, user_name, role FROM users_tb");
        echo "<h3>المستخدمين الموجودين:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>اسم المستخدم</th><th>الدور</th></tr>";
        
        while($user = $users->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr>";
            echo "<td>" . $user['id_user'] . "</td>";
            echo "<td>" . $user['user_name'] . "</td>";
            echo "<td>" . $user['role'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // اختبار تسجيل دخول مع بيانات تجريبية
        echo "<h3>اختبار تسجيل الدخول:</h3>";
        echo "<form method='POST'>";
        echo "<input type='text' name='test_user' placeholder='اسم المستخدم' required><br><br>";
        echo "<input type='password' name='test_pass' placeholder='كلمة المرور' required><br><br>";
        echo "<button type='submit' name='test_login'>اختبار تسجيل الدخول</button>";
        echo "</form>";
        
        if(isset($_POST['test_login'])) {
            $testUser = trim($_POST['test_user']);
            $testPass = trim($_POST['test_pass']);
            
            $login = $pdo->prepare("SELECT * FROM users_tb WHERE user_name=:u_user AND user_pass=:u_pass");
            $login->bindParam('u_user', $testUser);
            $login->bindParam('u_pass', $testPass);
            $login->execute();
            
            echo "<h4>نتيجة الاختبار:</h4>";
            echo "<p>اسم المستخدم المدخل: " . htmlspecialchars($testUser) . "</p>";
            echo "<p>كلمة المرور المدخلة: " . htmlspecialchars($testPass) . "</p>";
            echo "<p>عدد النتائج: " . $login->rowCount() . "</p>";
            
            if($login->rowCount() === 1) {
                $userObj = $login->fetchObject();
                echo "<p style='color: green;'>✅ تم العثور على المستخدم!</p>";
                echo "<p>الدور: " . $userObj->role . "</p>";
            } else {
                echo "<p style='color: red;'>❌ لم يتم العثور على المستخدم</p>";
                
                // التحقق من وجود اسم المستخدم فقط
                $checkUser = $pdo->prepare("SELECT * FROM users_tb WHERE user_name=:u_user");
                $checkUser->bindParam('u_user', $testUser);
                $checkUser->execute();
                
                if($checkUser->rowCount() > 0) {
                    echo "<p style='color: orange;'>⚠️ اسم المستخدم موجود لكن كلمة المرور خاطئة</p>";
                } else {
                    echo "<p style='color: red;'>❌ اسم المستخدم غير موجود</p>";
                }
            }
        }
        
    } else {
        echo "<h3>❌ جدول المستخدمين غير موجود</h3>";
    }
    
} catch(PDOException $e) {
    echo "<h2 style='color: red;'>❌ خطأ في الاتصال بقاعدة البيانات:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>
