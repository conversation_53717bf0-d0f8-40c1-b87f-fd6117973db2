<?php
session_start();
if(!isset($_SESSION['user'])) {
    header("Location: ../index.php");
    exit;
}

if($_SESSION['user']->role != 'Admin') {
    header("Location: ../index.php");
    exit;
}

include "addon/dbcon.php";
include "../includes/notifications.php";

$user_id = $_SESSION['user']->id_user;

// تمييز إشعار كمقروء إذا تم النقر عليه
if(isset($_GET['mark_read']) && is_numeric($_GET['mark_read'])) {
    markNotificationAsRead($con, $_GET['mark_read'], $user_id);
    header("Location: notifications.php");
    exit;
}

// تمييز جميع الإشعارات كمقروءة
if(isset($_GET['mark_all_read'])) {
    markAllNotificationsAsRead($con, $user_id);
    header("Location: notifications.php");
    exit;
}

// الحصول على جميع الإشعارات
$notifications = getRecentNotifications($con, $user_id, 50);
$unread_count = getUnreadNotificationsCount($con, $user_id);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإشعارات</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="icon" href="css/icon.ico">
    <?php include "addon/topbar.php" ?>
    <?php include "../includes/notification_component.php"; ?>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
            direction: rtl;
        }
        
        .notifications-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        
        .notifications-header {
            background: white;
            border-radius: 15px 15px 0 0;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .notifications-body {
            background: white;
            border-radius: 0 0 15px 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            max-height: 600px;
            overflow-y: auto;
        }
        
        .notification-item {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .notification-item:hover {
            background: #f8f9fa;
        }
        
        .notification-item.unread {
            background: linear-gradient(90deg, #e3f2fd 0%, #f8f9fa 100%);
            border-right: 5px solid #2196f3;
        }
        
        .notification-item:last-child {
            border-bottom: none;
        }
        
        .notification-header-item {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 10px;
        }
        
        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }
        
        .notification-content {
            flex: 1;
        }
        
        .notification-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
            font-size: 16px;
        }
        
        .notification-message {
            color: #495057;
            line-height: 1.5;
            font-size: 14px;
        }
        
        .notification-time {
            color: #6c757d;
            font-size: 12px;
            margin-top: 5px;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 48px;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        .btn-mark-all {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn-mark-all:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .back-btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        
        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="notifications-container">
        <a href="home.php" class="back-btn">
            <i class="fas fa-arrow-right"></i>
            العودة للرئيسية
        </a>
        
        <div class="notifications-header">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <h4 style="margin: 0; color: #2c3e50;">
                    <i class="fas fa-bell" style="color: #667eea; margin-left: 10px;"></i>
                    الإشعارات
                    <?php if($unread_count > 0): ?>
                        <span class="badge badge-primary" style="background: #dc3545; margin-right: 10px;">
                            <?php echo $unread_count; ?> جديد
                        </span>
                    <?php endif; ?>
                </h4>
                
                <?php if($unread_count > 0): ?>
                    <a href="?mark_all_read=1" class="btn-mark-all">
                        <i class="fas fa-check-double"></i> تمييز الكل كمقروء
                    </a>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="notifications-body">
            <?php if(empty($notifications)): ?>
                <div class="empty-state">
                    <i class="fas fa-bell-slash"></i>
                    <h5>لا توجد إشعارات</h5>
                    <p>ستظهر هنا جميع الإشعارات المتعلقة بالطلبات الجديدة</p>
                </div>
            <?php else: ?>
                <?php foreach($notifications as $notification): ?>
                    <div class="notification-item <?php echo $notification['is_read'] ? '' : 'unread'; ?>"
                         onclick="window.location.href='?mark_read=<?php echo $notification['id_notification']; ?>'">
                        <div class="notification-header-item">
                            <div class="notification-icon" style="background: <?php echo getNotificationColor($notification['type']); ?>">
                                <i class="<?php echo getNotificationIcon($notification['type']); ?>"></i>
                            </div>
                            <div class="notification-content">
                                <div class="notification-title"><?php echo htmlspecialchars($notification['title']); ?></div>
                                <div class="notification-message"><?php echo htmlspecialchars($notification['message']); ?></div>
                                <div class="notification-time">
                                    <i class="fas fa-clock"></i>
                                    <?php echo formatNotificationTime($notification['created_at']); ?>
                                </div>
                            </div>
                            <?php if(!$notification['is_read']): ?>
                                <div style="width: 10px; height: 10px; background: #dc3545; border-radius: 50%;"></div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
