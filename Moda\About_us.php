<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Mod"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ماذا عنا</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <script src="js/all.js"></script>
    <link rel="icon" href="css/icon.ico">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php" ?>
   </head>
   <style>
    
   </style>
   <body>
   <p class=pag2>
          تم بناء وتصميم هذا البرنامج من قبل المبرمج 
          <br> م.علي الربيعي 
          <br>
           مبني البرنامج على متطلبات خاصة
          وبالامكان التطوير على بيئة النظام حسب طلب المستخدم
          <br>
          يمكنك التواصل  معنا
       </p>
       <br>
       <div class='continICO'>
       <div class="divicon">
        <div class="wrappericon">
         <div class="button">
         <a href="https://facebook.com/Ali.M.Jable" style="text-decoration: none;color:black;"><div class="icon">
           <i class="fab fa-facebook-f"></i>
            </div>
            <span>Facebook</span></a>
         </div>
         <div class="button">
         <a href="https://wa.me/9647739046665" style="text-decoration: none;color:black;"><div class="icon">
               <i class="fab fa-whatsapp"></i>
            </div>
            <span>whatsapp</span></a>
         </div>
         <div class="button">
         <a href="https://www.instagram.com/alimahdi95_n/" style="text-decoration: none;color:black;"> <div class="icon">
               <i class="fab fa-instagram"></i>
            </div>
            <span>Instagram</span></a>
         </div>
      </div>
      </div>
</div>
</body>
<script src="js/all.js" crossorigin="anonymous"></script>
</body>
</html>