<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اجمالي عدد المصروفات </title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
    <script src="js/jquery.min.js"></script>
    <script src="js/jquery.dataTables.min.js"></script>
    <link rel="stylesheet" href="css/jquery.dataTables.min.css">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php" ?>
      <?php include "addon/dbcon.php" ?>
   </head>
    <body>
    <table class="table" id="Table" style="text-align:center;">
  <thead>
    <tr>
      <th scope="col">مستخدم الحضانة</th>
      <th scope="col"> تاريخ الصرف</th>
      <th scope="col">قيمة المصروف</th>
      <th scope="col">وصف  المصروف </th>  
    </tr>
  </thead>
  <tbody id="myTable">
    <?php
      
      $sql="SELECT * FROM depit_tb,users_tb WHERE depit_tb.userID=users_tb.id_user ";
      $result=mysqli_query($con,$sql);
      if($result){
       while($row=mysqli_fetch_assoc($result)) {
          $user_name=$row['user_name'];
          $depit_note=$row['depit_note'];
          $depit_date=$row['depit_date'];
          $depit_cash=number_format($row['depit_cash']);
          echo '<tr>
          <td> '.$user_name.' </td>
          <td>'.$depit_date.'</td>
          <td> IQD '.$depit_cash.'  </td>
          <td>'.$depit_note.'</td>

          
          </tr>
        ';
         }
      }
  

    ?>
   
  </tbody>
</table>
   </body>
<script>
  $(document).ready(function () {
    $("#Table").DataTable();
  });
</script>
</html>