<?php
session_start();
header('Content-Type: application/json');

if(!isset($_SESSION['user'])) {
    echo json_encode(['error' => 'غير مصرح']);
    exit;
}

include "../Users/<USER>/dbcon.php";
include "../includes/notifications.php";

$action = $_GET['action'] ?? '';
$user_id = $_SESSION['user']->id_user;
$user_role = $_SESSION['user']->role;

switch($action) {
    case 'get_counts':
        $unread_notifications = getUnreadNotificationsCount($con, $user_id);
        $new_needs = getNewNeedsCount($con, $user_role);
        $new_leaves = getNewLeavesCount($con, $user_role);
        
        echo json_encode([
            'unread_notifications' => $unread_notifications,
            'new_needs' => $new_needs,
            'new_leaves' => $new_leaves
        ]);
        break;
        
    case 'get_notifications':
        $notifications = getRecentNotifications($con, $user_id, 20);
        $formatted_notifications = [];
        
        foreach($notifications as $notification) {
            $formatted_notifications[] = [
                'id' => $notification['id_notification'],
                'title' => $notification['title'],
                'message' => $notification['message'],
                'type' => $notification['type'],
                'is_read' => $notification['is_read'],
                'time' => formatNotificationTime($notification['created_at']),
                'icon' => getNotificationIcon($notification['type']),
                'color' => getNotificationColor($notification['type'])
            ];
        }
        
        echo json_encode($formatted_notifications);
        break;
        
    case 'mark_read':
        $notification_id = $_POST['notification_id'] ?? 0;
        $success = markNotificationAsRead($con, $notification_id, $user_id);
        echo json_encode(['success' => $success]);
        break;
        
    case 'mark_all_read':
        $success = markAllNotificationsAsRead($con, $user_id);
        echo json_encode(['success' => $success]);
        break;
        
    case 'get_admin_counts':
        if($user_role == 'Admin' || $user_role == 'Mod') {
            // عدد طلبات الاحتياجات الجديدة
            $needs_stmt = $con->prepare("SELECT COUNT(*) as count FROM needs_requests WHERE status = 'قيد المراجعة'");
            $needs_stmt->execute();
            $needs_result = $needs_stmt->get_result();
            $needs_count = $needs_result->fetch_assoc()['count'];
            
            // عدد طلبات الإجازة الجديدة
            $leaves_stmt = $con->prepare("SELECT COUNT(*) as count FROM leave_requests WHERE status = 'قيد المراجعة'");
            $leaves_stmt->execute();
            $leaves_result = $leaves_stmt->get_result();
            $leaves_count = $leaves_result->fetch_assoc()['count'];
            
            echo json_encode([
                'needs_count' => $needs_count,
                'leaves_count' => $leaves_count
            ]);
        } else {
            echo json_encode(['error' => 'غير مصرح']);
        }
        break;
        
    case 'get_user_responses':
        if($user_role == 'User') {
            // عدد الردود الجديدة على الاحتياجات
            $needs_responses_stmt = $con->prepare("
                SELECT COUNT(*) as count 
                FROM needs_requests 
                WHERE user_id = ? 
                AND status != 'قيد المراجعة' 
                AND response_date > (
                    SELECT COALESCE(MAX(created_at), '1970-01-01') 
                    FROM notifications 
                    WHERE user_id = ? 
                    AND type = 'need_response' 
                    AND is_read = 1
                )
            ");
            $needs_responses_stmt->bind_param("ii", $user_id, $user_id);
            $needs_responses_stmt->execute();
            $needs_responses_result = $needs_responses_stmt->get_result();
            $needs_responses_count = $needs_responses_result->fetch_assoc()['count'];
            
            // عدد الردود الجديدة على الإجازات
            $leaves_responses_stmt = $con->prepare("
                SELECT COUNT(*) as count 
                FROM leave_requests 
                WHERE user_id = ? 
                AND status != 'قيد المراجعة' 
                AND response_date > (
                    SELECT COALESCE(MAX(created_at), '1970-01-01') 
                    FROM notifications 
                    WHERE user_id = ? 
                    AND type = 'leave_response' 
                    AND is_read = 1
                )
            ");
            $leaves_responses_stmt->bind_param("ii", $user_id, $user_id);
            $leaves_responses_stmt->execute();
            $leaves_responses_result = $leaves_responses_stmt->get_result();
            $leaves_responses_count = $leaves_responses_result->fetch_assoc()['count'];
            
            echo json_encode([
                'needs_responses' => $needs_responses_count,
                'leaves_responses' => $leaves_responses_count
            ]);
        } else {
            echo json_encode(['error' => 'غير مصرح']);
        }
        break;
        
    default:
        echo json_encode(['error' => 'إجراء غير صحيح']);
        break;
}
?>
