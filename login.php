<?php
session_start();

// التحقق من وجود جلسة مستخدم نشطة
if(isset($_SESSION['user'])){
    // إذا كان المستخدم مسجل دخول بالفعل، توجيهه إلى الصفحة المناسبة
    if($_SESSION['user']->role === "Admin"){
        header("location: Admin/home.php");
        exit();
    } elseif($_SESSION['user']->role === "User"){
        header("location: Users/home.php");
        exit();
    } elseif($_SESSION['user']->role === "Mod"){
        header("location: Moda/home.php");
        exit();
    }
}

$user='kidzrcle_rwda';
$pass='kidzrcle_rwda';
$pdo= new PDO ('mysql:host=localhost;dbname=kidzrcle_rwda',$user,$pass);
$login=$pdo->prepare("SELECT * FROM users_tb WHERE user_name=:u_user AND user_pass=:u_pass");
$login->bindParam('u_user',$_POST['u_user']);
$login->bindParam('u_pass',$_POST['u_pass']);
$login->execute();
if(isset($_POST['login'])){
if($login->rowCount()===1){
$user=$login->fetchObject();
$_SESSION['user']=$user;
if($user->role==='Admin')
{
    echo "<div class='alert alert-success'>مرحباً بك أدمن! تم تسجيل الدخول بنجاح</div>";
    echo "<script>setTimeout(function(){ window.location.href = 'Admin/home.php'; }, 2000);</script>";
}elseif($user->role==='User'){
    echo "<div class='alert alert-success'>مرحباً بك مستخدم! تم تسجيل الدخول بنجاح</div>";
    echo "<script>setTimeout(function(){ window.location.href = 'Users/home.php'; }, 2000);</script>";
}elseif($user->role==="Mod"){
    echo "<div class='alert alert-success'>مرحباً بك مدقق! تم تسجيل الدخول بنجاح</div>";
    echo "<script>setTimeout(function(){ window.location.href = 'Moda/home.php'; }, 2000);</script>";
}

}else{
    echo "<div class='alert alert-danger'>خطأ في اسم المستخدم أو كلمة المرور</div>";

}
}


?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="Admin/css/styles.css">
    <link rel="stylesheet" href="Admin/css/bootstrap.min.css">
    <link rel="stylesheet" href="Admin/css/datatables.min.css">
    <link rel="stylesheet" href="Admin/css/all.min.css">
    <link rel="icon" href="icon.ico">
    <title>تسجيل دخول - كيدز أكاديمي</title>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            font-family: 'Arial', sans-serif;
        }
        .continC {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
            backdrop-filter: blur(10px);
        }
        .input-box {
            text-align: center;
        }
        .input-box h2 {
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
            font-weight: bold;
        }
        .input-box label {
            display: block;
            text-align: right;
            margin-bottom: 8px;
            color: #555;
            font-weight: bold;
        }
        .input-box input {
            width: 100%;
            padding: 12px 15px;
            margin-bottom: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }
        .input-box input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 10px rgba(102, 126, 234, 0.3);
        }
        .btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.3s;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .footer_p {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            text-align: center;
            padding: 15px;
            margin: 0;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            text-align: center;
            font-weight: bold;
        }
        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .alert-danger {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
<div class='continC'>
    <form method="POST">
        <div class='input-box'>
            <h2>تسجيل الدخول</h2>
            <label for="user">اسم المستخدم</label>
            <input type="text" placeholder="أدخل اسم المستخدم" name="u_user" required>

            <label for="pass">كلمة المرور</label>
            <input type="password" placeholder="أدخل كلمة المرور" name="u_pass" required>

            <button class="btn" name="login" type="submit">تسجيل الدخول</button>
        </div>
    </form>
</div>
<footer class="footer_p">
    <p>جميع الحقوق محفوظة لمؤسسة كيدز أكاديمي © 2024</p>
</footer>
</html>