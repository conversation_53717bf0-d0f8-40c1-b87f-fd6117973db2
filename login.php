<?php
session_start();

// التحقق من وجود جلسة مستخدم نشطة
if(isset($_SESSION['user'])){
    // إذا كان المستخدم مسجل دخول بالفعل، توجيهه إلى الصفحة المناسبة
    if($_SESSION['user']->role === "Admin"){
        header("location: Admin/home.php");
        exit();
    } elseif($_SESSION['user']->role === "User"){
        header("location: Users/home.php");
        exit();
    } elseif($_SESSION['user']->role === "Mod"){
        header("location: Moda/home.php");
        exit();
    }
}

$loginMessage = '';
$loginStatus = '';

if(isset($_POST['login'])){
    $user='kidzrcle_rwda';
    $pass='kidzrcle_rwda';
    $pdo= new PDO ('mysql:host=localhost;dbname=kidzrcle_rwda',$user,$pass);
    $login=$pdo->prepare("SELECT * FROM users_tb WHERE user_name=:u_user AND user_pass=:u_pass");
    $login->bindParam('u_user',$_POST['u_user']);
    $login->bindParam('u_pass',$_POST['u_pass']);
    $login->execute();

    if($login->rowCount()===1){
        $user=$login->fetchObject();
        $_SESSION['user']=$user;
        if($user->role==='Admin'){
            $loginMessage = "مرحباً بك أدمن! تم تسجيل الدخول بنجاح";
            $loginStatus = 'success';
            echo "<script>setTimeout(function(){ window.location.href = 'Admin/home.php'; }, 2000);</script>";
        }elseif($user->role==='User'){
            $loginMessage = "مرحباً بك مستخدم! تم تسجيل الدخول بنجاح";
            $loginStatus = 'success';
            echo "<script>setTimeout(function(){ window.location.href = 'Users/home.php'; }, 2000);</script>";
        }elseif($user->role==="Mod"){
            $loginMessage = "مرحباً بك مدقق! تم تسجيل الدخول بنجاح";
            $loginStatus = 'success';
            echo "<script>setTimeout(function(){ window.location.href = 'Moda/home.php'; }, 2000);</script>";
        }
    }else{
        $loginMessage = "خطأ في اسم المستخدم أو كلمة المرور";
        $loginStatus = 'error';
    }
}


?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="Admin/css/all.min.css">
    <link rel="icon" href="icon.ico">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <title>تسجيل دخول - الروضة الأصلي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        /* خلفية متحركة */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><radialGradient id="a" cx="50%" cy="50%" r="50%"><stop offset="0%" stop-color="rgba(255,255,255,0.1)"/><stop offset="100%" stop-color="rgba(255,255,255,0)"/></radialGradient></defs><circle cx="20" cy="20" r="10" fill="url(%23a)"><animate attributeName="cy" values="20;80;20" dur="3s" repeatCount="indefinite"/></circle><circle cx="80" cy="80" r="15" fill="url(%23a)"><animate attributeName="cx" values="80;20;80" dur="4s" repeatCount="indefinite"/></circle><circle cx="50" cy="50" r="8" fill="url(%23a)"><animate attributeName="r" values="8;20;8" dur="2s" repeatCount="indefinite"/></circle></svg>') no-repeat center center;
            background-size: cover;
            animation: float 6s ease-in-out infinite;
            z-index: 1;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .login-container {
            position: relative;
            z-index: 10;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            padding: 50px 40px;
            width: 100%;
            max-width: 450px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: slideIn 0.8s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .logo-section {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .logo-icon i {
            font-size: 35px;
            color: white;
        }

        .welcome-text {
            color: #333;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #666;
            font-size: 16px;
            margin-bottom: 30px;
        }

        .form-group {
            position: relative;
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            color: #555;
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .input-wrapper {
            position: relative;
        }

        .form-control {
            width: 100%;
            padding: 15px 20px 15px 50px;
            border: 2px solid #e1e5e9;
            border-radius: 15px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }

        .input-icon {
            position: absolute;
            left: 18px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 18px;
            transition: color 0.3s ease;
        }

        .form-control:focus + .input-icon {
            color: #667eea;
        }

        .login-btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        }

        .login-btn:active {
            transform: translateY(-1px);
        }

        /* إشعارات جميلة */
        .notification {
            position: fixed;
            top: 30px;
            right: 30px;
            padding: 20px 25px;
            border-radius: 15px;
            color: white;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transform: translateX(400px);
            transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 15px;
            max-width: 400px;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: linear-gradient(135deg, #4CAF50, #45a049);
        }

        .notification.error {
            background: linear-gradient(135deg, #f44336, #da190b);
        }

        .notification i {
            font-size: 24px;
        }

        .notification-content {
            flex: 1;
        }

        .notification-title {
            font-weight: 700;
            margin-bottom: 5px;
        }

        .notification-message {
            font-size: 14px;
            opacity: 0.9;
        }

        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            color: white;
            text-align: center;
            padding: 20px;
            font-size: 14px;
            z-index: 5;
        }

        .footer p {
            margin: 0;
            opacity: 0.9;
        }

        /* تأثيرات إضافية */
        .sparkle {
            position: absolute;
            width: 6px;
            height: 6px;
            background: white;
            border-radius: 50%;
            animation: sparkle 2s linear infinite;
        }

        @keyframes sparkle {
            0% { opacity: 0; transform: scale(0); }
            50% { opacity: 1; transform: scale(1); }
            100% { opacity: 0; transform: scale(0); }
        }

        /* تحسينات للموبايل */
        @media (max-width: 768px) {
            .login-container {
                margin: 20px;
                padding: 40px 30px;
            }

            .notification {
                right: 20px;
                left: 20px;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <!-- عناصر متحركة للخلفية -->
    <div class="sparkle" style="top: 20%; left: 10%; animation-delay: 0s;"></div>
    <div class="sparkle" style="top: 60%; left: 80%; animation-delay: 1s;"></div>
    <div class="sparkle" style="top: 80%; left: 20%; animation-delay: 2s;"></div>
    <div class="sparkle" style="top: 30%; left: 70%; animation-delay: 1.5s;"></div>

    <div class="login-container">
        <div class="logo-section">
            <div class="logo-icon">
                <i class="fas fa-graduation-cap"></i>
            </div>
            <h1 class="welcome-text">أهلاً وسهلاً</h1>
            <p class="subtitle">مرحباً بك في الروضة الأصلي</p>
        </div>

        <form method="POST" id="loginForm">
            <div class="form-group">
                <label for="username">اسم المستخدم</label>
                <div class="input-wrapper">
                    <input type="text" id="username" name="u_user" class="form-control" placeholder="أدخل اسم المستخدم" required>
                    <i class="fas fa-user input-icon"></i>
                </div>
            </div>

            <div class="form-group">
                <label for="password">كلمة المرور</label>
                <div class="input-wrapper">
                    <input type="password" id="password" name="u_pass" class="form-control" placeholder="أدخل كلمة المرور" required>
                    <i class="fas fa-lock input-icon"></i>
                </div>
            </div>

            <button type="submit" name="login" class="login-btn">
                <i class="fas fa-sign-in-alt" style="margin-left: 10px;"></i>
                تسجيل الدخول
            </button>
        </form>
    </div>

    <footer class="footer">
        <p>جميع الحقوق محفوظة لمؤسسة الروضة الأصلي © 2024</p>
    </footer>

    <script>
        // عرض الإشعارات
        function showNotification(message, type, title) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;

            const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle';

            notification.innerHTML = `
                <i class="${icon}"></i>
                <div class="notification-content">
                    <div class="notification-title">${title}</div>
                    <div class="notification-message">${message}</div>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 500);
            }, 4000);
        }

        // عرض الإشعار إذا كان هناك رسالة
        <?php if($loginMessage): ?>
            <?php if($loginStatus === 'success'): ?>
                showNotification('<?php echo $loginMessage; ?>', 'success', 'تم بنجاح!');
            <?php else: ?>
                showNotification('<?php echo $loginMessage; ?>', 'error', 'خطأ!');
            <?php endif; ?>
        <?php endif; ?>

        // تأثيرات إضافية للنموذج
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const btn = this.querySelector('.login-btn');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-left: 10px;"></i>جاري التحقق...';
            btn.disabled = true;
        });

        // تأثير التركيز على الحقول
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>