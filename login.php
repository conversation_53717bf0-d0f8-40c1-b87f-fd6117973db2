<?php
session_start();

// التحقق من وجود جلسة مستخدم نشطة
if(isset($_SESSION['user'])){
    // إذا كان المستخدم مسجل دخول بالفعل، توجيهه إلى الصفحة المناسبة
    if($_SESSION['user']->role === "Admin"){
        header("location: Admin/home.php");
        exit();
    } elseif($_SESSION['user']->role === "User"){
        header("location: Users/home.php");
        exit();
    } elseif($_SESSION['user']->role === "Mod"){
        header("location: Moda/home.php");
        exit();
    }
}

$loginMessage = '';
$loginStatus = '';

if(isset($_POST['login'])){
    try {
        $user='kidzrcle_rwda';
        $pass='kidzrcle_rwda';
        $pdo= new PDO ('mysql:host=localhost;dbname=kidzrcle_rwda;charset=utf8',$user,$pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $login=$pdo->prepare("SELECT * FROM users_tb WHERE user_name=:u_user AND user_pass=:u_pass");
        $login->bindParam('u_user',$_POST['u_user']);
        $login->bindParam('u_pass',$_POST['u_pass']);
        $login->execute();

        if($login->rowCount()===1){
            $userObj=$login->fetchObject();
            $_SESSION['user']=$userObj;
            if($userObj->role==='Admin'){
                $loginMessage = "مرحباً بك مدير النظام! تم تسجيل الدخول بنجاح";
                $loginStatus = 'success';
                echo "<script>setTimeout(function(){ window.location.href = 'Admin/home.php'; }, 1500);</script>";
            }elseif($userObj->role==='User'){
                $loginMessage = "مرحباً بك! تم تسجيل الدخول بنجاح";
                $loginStatus = 'success';
                echo "<script>setTimeout(function(){ window.location.href = 'Users/home.php'; }, 1500);</script>";
            }elseif($userObj->role==="Mod"){
                $loginMessage = "مرحباً بك مشرف! تم تسجيل الدخول بنجاح";
                $loginStatus = 'success';
                echo "<script>setTimeout(function(){ window.location.href = 'Moda/home.php'; }, 1500);</script>";
            }
        }else{
            $loginMessage = "خطأ في اسم المستخدم أو كلمة المرور";
            $loginStatus = 'error';
        }
    } catch(PDOException $e) {
        $loginMessage = "خطأ في الاتصال بقاعدة البيانات";
        $loginStatus = 'error';
    }
}


?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="Admin/css/all.min.css">
    <link rel="icon" href="icon.ico">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <title>تسجيل دخول - نظام إدارة الروضة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* خلفية متحركة مبهرة */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
            animation: float 8s ease-in-out infinite;
            z-index: 1;
        }

        body::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><radialGradient id="sparkle" cx="50%" cy="50%" r="50%"><stop offset="0%" stop-color="rgba(255,255,255,0.8)"/><stop offset="100%" stop-color="rgba(255,255,255,0)"/></radialGradient></defs><circle cx="10" cy="10" r="2" fill="url(%23sparkle)"><animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite"/></circle><circle cx="90" cy="20" r="1.5" fill="url(%23sparkle)"><animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="indefinite" begin="0.5s"/></circle><circle cx="20" cy="90" r="1" fill="url(%23sparkle)"><animate attributeName="opacity" values="0;1;0" dur="2.5s" repeatCount="indefinite" begin="1s"/></circle><circle cx="80" cy="80" r="2.5" fill="url(%23sparkle)"><animate attributeName="opacity" values="0;1;0" dur="1.8s" repeatCount="indefinite" begin="1.5s"/></circle></svg>');
            background-size: 200px 200px;
            animation: sparkleMove 20s linear infinite;
            z-index: 2;
            pointer-events: none;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-30px) rotate(180deg); }
        }

        @keyframes sparkleMove {
            0% { background-position: 0% 0%; }
            100% { background-position: 100% 100%; }
        }

        .login-container {
            position: relative;
            z-index: 10;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(25px);
            border-radius: 30px;
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2),
                        inset 0 1px 0 rgba(255, 255, 255, 0.3),
                        0 0 50px rgba(255, 255, 255, 0.1);
            padding: 60px 50px;
            width: 100%;
            max-width: 480px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            animation: slideIn 1s ease-out, glow 3s ease-in-out infinite alternate;
            overflow: hidden;
        }

        @keyframes glow {
            0% { box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3), 0 0 50px rgba(255, 255, 255, 0.1); }
            100% { box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3), 0 0 80px rgba(255, 255, 255, 0.3); }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .logo-section {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo-icon {
            width: 120px;
            height: 120px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 400% 400%;
            animation: gradientShift 3s ease infinite, pulse 2s infinite, rotate 10s linear infinite;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2),
                        0 0 30px rgba(255, 255, 255, 0.3),
                        inset 0 2px 0 rgba(255, 255, 255, 0.3);
            border: 3px solid rgba(255, 255, 255, 0.4);
            position: relative;
            overflow: hidden;
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: rotate 3s linear infinite;
        }

        .logo-icon img {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            z-index: 2;
            position: relative;
            filter: drop-shadow(0 2px 8px rgba(0,0,0,0.3));
        }

        .logo-icon i {
            font-size: 45px;
            color: white;
            z-index: 2;
            position: relative;
            text-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.08); }
            100% { transform: scale(1); }
        }

        @keyframes rotate {
            100% { transform: rotate(360deg); }
        }

        .welcome-text {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            background-size: 400% 400%;
            animation: gradientShift 5s ease infinite;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 32px;
            font-weight: 800;
            margin-bottom: 15px;
            text-align: center;
            text-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 18px;
            margin-bottom: 35px;
            text-align: center;
            font-weight: 500;
            text-shadow: 0 1px 3px rgba(0,0,0,0.3);
        }

        .form-group {
            position: relative;
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            color: #555;
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .input-wrapper {
            position: relative;
        }

        .form-control {
            width: 100%;
            padding: 18px 25px 18px 55px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            font-size: 16px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            color: #333;
            font-weight: 500;
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.7);
            font-weight: 400;
        }

        .form-control:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.8);
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 0 30px rgba(255, 255, 255, 0.3),
                        inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transform: translateY(-3px) scale(1.02);
        }

        .input-icon {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.6);
            font-size: 20px;
            transition: all 0.4s ease;
            z-index: 2;
        }

        .form-control:focus + .input-icon {
            color: rgba(255, 255, 255, 0.9);
            transform: translateY(-50%) scale(1.1);
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .login-btn {
            width: 100%;
            padding: 20px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 400% 400%;
            animation: gradientShift 3s ease infinite;
            color: white;
            border: none;
            border-radius: 20px;
            font-size: 20px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.6s ease;
        }

        .login-btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s ease, height 0.6s ease;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover::after {
            width: 300px;
            height: 300px;
        }

        .login-btn:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3),
                        0 0 30px rgba(255, 255, 255, 0.2);
        }

        .login-btn:active {
            transform: translateY(-2px) scale(0.98);
        }

        /* إشعارات مبهرة */
        .notification {
            position: fixed;
            top: 30px;
            right: 30px;
            padding: 25px 30px;
            border-radius: 20px;
            color: white;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3),
                        0 0 20px rgba(255, 255, 255, 0.1);
            transform: translateX(450px) scale(0.8);
            transition: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 18px;
            max-width: 420px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .notification.show {
            transform: translateX(0) scale(1);
        }

        .notification.success {
            background: linear-gradient(135deg, #00C851, #007E33);
            animation: successPulse 2s ease infinite;
        }

        .notification.error {
            background: linear-gradient(135deg, #ff4444, #CC0000);
            animation: errorShake 0.5s ease;
        }

        @keyframes successPulse {
            0%, 100% { box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3), 0 0 20px rgba(0, 200, 81, 0.3); }
            50% { box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3), 0 0 40px rgba(0, 200, 81, 0.6); }
        }

        @keyframes errorShake {
            0%, 100% { transform: translateX(0) scale(1); }
            25% { transform: translateX(-5px) scale(1); }
            75% { transform: translateX(5px) scale(1); }
        }

        .notification i {
            font-size: 24px;
        }

        .notification-content {
            flex: 1;
        }

        .notification-title {
            font-weight: 700;
            margin-bottom: 5px;
        }

        .notification-message {
            font-size: 14px;
            opacity: 0.9;
        }

        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            color: white;
            text-align: center;
            padding: 20px;
            font-size: 14px;
            z-index: 5;
        }

        .footer p {
            margin: 0;
            opacity: 0.9;
        }

        /* تأثيرات إضافية */
        .sparkle {
            position: absolute;
            width: 6px;
            height: 6px;
            background: white;
            border-radius: 50%;
            animation: sparkle 2s linear infinite;
        }

        @keyframes sparkle {
            0% { opacity: 0; transform: scale(0); }
            50% { opacity: 1; transform: scale(1); }
            100% { opacity: 0; transform: scale(0); }
        }

        /* دوائر متحركة إضافية */
        .floating-circle {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: floatCircle 8s ease-in-out infinite;
            z-index: 1;
        }

        @keyframes floatCircle {
            0%, 100% {
                transform: translateY(0px) translateX(0px) scale(1);
                opacity: 0.3;
            }
            25% {
                transform: translateY(-30px) translateX(20px) scale(1.2);
                opacity: 0.6;
            }
            50% {
                transform: translateY(-10px) translateX(-15px) scale(0.8);
                opacity: 0.4;
            }
            75% {
                transform: translateY(-40px) translateX(10px) scale(1.1);
                opacity: 0.7;
            }
        }

        /* تحسينات للموبايل */
        @media (max-width: 768px) {
            .login-container {
                margin: 20px;
                padding: 50px 35px;
            }

            .notification {
                right: 20px;
                left: 20px;
                max-width: none;
            }

            .logo-icon {
                width: 100px;
                height: 100px;
            }

            .welcome-text {
                font-size: 28px;
            }

            .subtitle {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <!-- عناصر متحركة مبهرة للخلفية -->
    <div class="sparkle" style="top: 15%; left: 8%; animation-delay: 0s;"></div>
    <div class="sparkle" style="top: 65%; left: 85%; animation-delay: 1s;"></div>
    <div class="sparkle" style="top: 85%; left: 15%; animation-delay: 2s;"></div>
    <div class="sparkle" style="top: 25%; left: 75%; animation-delay: 1.5s;"></div>
    <div class="sparkle" style="top: 45%; left: 5%; animation-delay: 0.8s;"></div>
    <div class="sparkle" style="top: 10%; left: 50%; animation-delay: 2.2s;"></div>
    <div class="sparkle" style="top: 75%; left: 60%; animation-delay: 1.8s;"></div>
    <div class="sparkle" style="top: 35%; left: 90%; animation-delay: 0.3s;"></div>

    <!-- دوائر متحركة إضافية -->
    <div class="floating-circle" style="top: 20%; left: 20%; animation-delay: 0s;"></div>
    <div class="floating-circle" style="top: 70%; left: 70%; animation-delay: 2s;"></div>
    <div class="floating-circle" style="top: 40%; left: 80%; animation-delay: 1s;"></div>

    <div class="login-container">
        <div class="logo-section">
            <div class="logo-icon">
                <img src="Admin/css/logooo.png" alt="شعار النظام" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                <i class="fas fa-child" style="display: none;"></i>
            </div>
            <h1 class="welcome-text">أهلاً وسهلاً</h1>
            <p class="subtitle">نظام إدارة الروضة المتطور</p>
        </div>

        <form method="POST" id="loginForm">
            <div class="form-group">
                <label for="username">اسم المستخدم</label>
                <div class="input-wrapper">
                    <input type="text" id="username" name="u_user" class="form-control" placeholder="أدخل اسم المستخدم" required>
                    <i class="fas fa-user input-icon"></i>
                </div>
            </div>

            <div class="form-group">
                <label for="password">كلمة المرور</label>
                <div class="input-wrapper">
                    <input type="password" id="password" name="u_pass" class="form-control" placeholder="أدخل كلمة المرور" required>
                    <i class="fas fa-lock input-icon"></i>
                </div>
            </div>

            <button type="submit" name="login" class="login-btn">
                <i class="fas fa-sign-in-alt" style="margin-left: 10px;"></i>
                تسجيل الدخول
            </button>
        </form>
    </div>

    <footer class="footer">
        <p>جميع الحقوق محفوظة لنظام إدارة الروضة © 2024</p>
    </footer>

    <script>
        // عرض الإشعارات
        function showNotification(message, type, title) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;

            const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle';

            notification.innerHTML = `
                <i class="${icon}"></i>
                <div class="notification-content">
                    <div class="notification-title">${title}</div>
                    <div class="notification-message">${message}</div>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 500);
            }, 4000);
        }

        // عرض الإشعار إذا كان هناك رسالة
        <?php if($loginMessage): ?>
            <?php if($loginStatus === 'success'): ?>
                showNotification('<?php echo $loginMessage; ?>', 'success', 'تم بنجاح!');
            <?php else: ?>
                showNotification('<?php echo $loginMessage; ?>', 'error', 'خطأ!');
            <?php endif; ?>
        <?php endif; ?>

        // تأثيرات مبهرة للنموذج
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const btn = this.querySelector('.login-btn');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-left: 10px;"></i>جاري التحقق...';
            btn.disabled = true;
            btn.style.background = 'linear-gradient(45deg, #feca57, #ff9ff3, #54a0ff)';
            btn.style.animation = 'gradientShift 1s ease infinite';
        });

        // تأثيرات الحقول المحسنة
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.03)';
                this.parentElement.style.filter = 'drop-shadow(0 10px 20px rgba(255,255,255,0.2))';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
                this.parentElement.style.filter = 'none';
            });

            // تأثير الكتابة
            input.addEventListener('input', function() {
                if(this.value.length > 0) {
                    this.style.background = 'rgba(255, 255, 255, 0.25)';
                } else {
                    this.style.background = 'rgba(255, 255, 255, 0.1)';
                }
            });
        });

        // تأثير تتبع الماوس
        document.addEventListener('mousemove', function(e) {
            const sparkles = document.querySelectorAll('.sparkle');
            sparkles.forEach((sparkle, index) => {
                const speed = (index + 1) * 0.01;
                const x = e.clientX * speed;
                const y = e.clientY * speed;
                sparkle.style.transform = `translate(${x}px, ${y}px)`;
            });
        });

        // تأثير النقر على الشاشة
        document.addEventListener('click', function(e) {
            createRipple(e.clientX, e.clientY);
        });

        function createRipple(x, y) {
            const ripple = document.createElement('div');
            ripple.style.position = 'fixed';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.style.width = '10px';
            ripple.style.height = '10px';
            ripple.style.background = 'rgba(255, 255, 255, 0.6)';
            ripple.style.borderRadius = '50%';
            ripple.style.transform = 'translate(-50%, -50%)';
            ripple.style.animation = 'rippleEffect 0.8s ease-out forwards';
            ripple.style.pointerEvents = 'none';
            ripple.style.zIndex = '999';

            document.body.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 800);
        }

        // إضافة CSS للتأثير المتموج
        const style = document.createElement('style');
        style.textContent = `
            @keyframes rippleEffect {
                to {
                    width: 100px;
                    height: 100px;
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>