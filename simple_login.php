<?php
session_start();

// التحقق من وجود جلسة مستخدم نشطة
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role === "Admin"){
        header("location: Admin/home.php");
        exit();
    } elseif($_SESSION['user']->role === "User"){
        header("location: Users/home.php");
        exit();
    } elseif($_SESSION['user']->role === "Mod"){
        header("location: Moda/home.php");
        exit();
    }
}

$message = '';

if(isset($_POST['login'])){
    try {
        $user='kidzrcle_rwda';
        $pass='kidzrcle_rwda';
        $pdo= new PDO ('mysql:host=localhost;dbname=kidzrcle_rwda;charset=utf8',$user,$pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $username = trim($_POST['u_user']);
        $password = trim($_POST['u_pass']);
        
        echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px; border: 1px solid #ccc;'>";
        echo "<h3>معلومات التشخيص:</h3>";
        echo "<p>اسم المستخدم المدخل: " . htmlspecialchars($username) . "</p>";
        echo "<p>كلمة المرور المدخلة: " . htmlspecialchars($password) . "</p>";
        
        if(empty($username) || empty($password)) {
            $message = "يرجى إدخال اسم المستخدم وكلمة المرور";
        } else {
            $login=$pdo->prepare("SELECT * FROM users_tb WHERE user_name=:u_user AND user_pass=:u_pass");
            $login->bindParam('u_user', $username);
            $login->bindParam('u_pass', $password);
            $login->execute();
            
            echo "<p>عدد النتائج من قاعدة البيانات: " . $login->rowCount() . "</p>";
            
            if($login->rowCount()===1){
                $userObj=$login->fetchObject();
                $_SESSION['user']=$userObj;
                
                echo "<p style='color: green;'>✅ تم العثور على المستخدم!</p>";
                echo "<p>الدور: " . $userObj->role . "</p>";
                
                if($userObj->role==='Admin'){
                    $message = "مرحباً بك مدير النظام! تم تسجيل الدخول بنجاح";
                    echo "<script>setTimeout(function(){ window.location.href = 'Admin/home.php'; }, 3000);</script>";
                }elseif($userObj->role==='User'){
                    $message = "مرحباً بك! تم تسجيل الدخول بنجاح";
                    echo "<script>setTimeout(function(){ window.location.href = 'Users/home.php'; }, 3000);</script>";
                }elseif($userObj->role==="Mod"){
                    $message = "مرحباً بك مشرف! تم تسجيل الدخول بنجاح";
                    echo "<script>setTimeout(function(){ window.location.href = 'Moda/home.php'; }, 3000);</script>";
                }
            }else{
                // التحقق من وجود المستخدم
                $checkUser = $pdo->prepare("SELECT * FROM users_tb WHERE user_name=:u_user");
                $checkUser->bindParam('u_user', $username);
                $checkUser->execute();
                
                if($checkUser->rowCount() > 0) {
                    $message = "كلمة المرور غير صحيحة";
                    echo "<p style='color: orange;'>⚠️ اسم المستخدم موجود لكن كلمة المرور خاطئة</p>";
                } else {
                    $message = "اسم المستخدم غير موجود";
                    echo "<p style='color: red;'>❌ اسم المستخدم غير موجود</p>";
                }
            }
        }
        echo "</div>";
        
    } catch(PDOException $e) {
        $message = "خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage();
        echo "<div style='background: #ffcccc; padding: 10px; margin: 10px; border: 1px solid #ff0000;'>";
        echo "<p>خطأ: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-container { max-width: 400px; margin: 0 auto; padding: 20px; border: 1px solid #ccc; }
        input { width: 100%; padding: 10px; margin: 10px 0; }
        button { width: 100%; padding: 15px; background: #007cba; color: white; border: none; cursor: pointer; }
        .message { padding: 10px; margin: 10px 0; background: #f0f0f0; border: 1px solid #ccc; }
    </style>
</head>
<body>
    <div class="form-container">
        <h2>اختبار تسجيل الدخول</h2>
        
        <?php if($message): ?>
            <div class="message"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <form method="POST">
            <input type="text" name="u_user" placeholder="اسم المستخدم" required>
            <input type="password" name="u_pass" placeholder="كلمة المرور" required>
            <button type="submit" name="login">تسجيل الدخول</button>
        </form>
        
        <p><a href="test_db.php">اختبار قاعدة البيانات</a></p>
        <p><a href="login.php">العودة للصفحة الأساسية</a></p>
    </div>
</body>
</html>
