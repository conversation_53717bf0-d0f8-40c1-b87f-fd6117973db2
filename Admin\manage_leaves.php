<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin" || $_SESSION['user']->role==="Mod"){
        
    }else{
        header("location:../login.php",true);
        die("");
    }
}else{
    header("location:../login.php",true);
    die("");
}

include "addon/dbcon.php";

$message = '';
$messageType = '';

// معالجة الرد على طلب الإجازة
if(isset($_POST['respond_leave'])){
    try {
        $leave_id = $_POST['leave_id'];
        $status = $_POST['status'];
        $admin_response = trim($_POST['admin_response']);
        $response_by = $_SESSION['user']->id_user;
        $response_date = date('Y-m-d H:i:s');
        
        $stmt = $con->prepare("UPDATE leave_requests SET status = ?, admin_response = ?, response_by = ?, response_date = ? WHERE id_leave = ?");
        $stmt->bind_param("ssisi", $status, $admin_response, $response_by, $response_date, $leave_id);
        
        if($stmt->execute()) {
            $message = "تم تحديث حالة طلب الإجازة بنجاح";
            $messageType = 'success';
        } else {
            $message = "حدث خطأ أثناء تحديث الطلب";
            $messageType = 'error';
        }
    } catch(Exception $e) {
        $message = "حدث خطأ: " . $e->getMessage();
        $messageType = 'error';
    }
}

// فلترة البيانات
$where_conditions = [];
$params = [];
$param_types = "";

if(isset($_GET['from_date']) && !empty($_GET['from_date'])) {
    $where_conditions[] = "lr.request_date >= ?";
    $params[] = $_GET['from_date'];
    $param_types .= "s";
}

if(isset($_GET['to_date']) && !empty($_GET['to_date'])) {
    $where_conditions[] = "lr.request_date <= ?";
    $params[] = $_GET['to_date'];
    $param_types .= "s";
}

if(isset($_GET['user_id']) && !empty($_GET['user_id'])) {
    $where_conditions[] = "lr.user_id = ?";
    $params[] = $_GET['user_id'];
    $param_types .= "i";
}

if(isset($_GET['status']) && !empty($_GET['status'])) {
    $where_conditions[] = "lr.status = ?";
    $params[] = $_GET['status'];
    $param_types .= "s";
}

if(isset($_GET['leave_type']) && !empty($_GET['leave_type'])) {
    $where_conditions[] = "lr.leave_type = ?";
    $params[] = $_GET['leave_type'];
    $param_types .= "s";
}

$where_clause = "";
if(!empty($where_conditions)) {
    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
}

$query = "SELECT lr.*, u.user_name, ur.user_name as response_by_name 
          FROM leave_requests lr 
          JOIN users_tb u ON lr.user_id = u.id_user 
          LEFT JOIN users_tb ur ON lr.response_by = ur.id_user 
          $where_clause 
          ORDER BY lr.created_at DESC";

if(!empty($params)) {
    $stmt = $con->prepare($query);
    $stmt->bind_param($param_types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
} else {
    $result = $con->query($query);
}

// جلب قائمة المستخدمين للفلترة
$users_query = "SELECT id_user, user_name FROM users_tb WHERE role = 'User' ORDER BY user_name";
$users_result = $con->query($users_query);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة طلبات الإجازة</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="icon" href="css/icon.ico">
    <?php include "addon/topbar.php" ?>
    <?php include "../includes/notification_component.php"; ?>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
        }
        
        .manage-container {
            max-width: 1400px;
            margin: 50px auto;
            padding: 20px;
        }
        
        .page-header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .page-title {
            color: #2c3e50;
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .filter-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .form-control, .form-select {
            width: 100%;
            padding: 10px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            outline: none;
        }
        
        .filter-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .filter-btn:hover {
            background: linear-gradient(45deg, #5a6fd8, #6a4190);
            transform: translateY(-2px);
        }
        
        .leave-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .leave-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .employee-name {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-weight: bold;
            font-size: 12px;
        }
        
        .status-pending {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
            color: white;
        }
        
        .status-approved {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        
        .status-rejected {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
        }
        
        .leave-type-badge {
            background: linear-gradient(45deg, #17a2b8, #6f42c1);
            color: white;
            padding: 4px 8px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .date-range {
            background: #e9ecef;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
            text-align: center;
            font-weight: bold;
            border-right: 4px solid #667eea;
        }
        
        .leave-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #495057;
            font-size: 14px;
        }
        
        .info-item i {
            color: #667eea;
            width: 16px;
        }
        
        .leave-details {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            border-right: 4px solid #667eea;
        }
        
        .response-form {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin-top: 15px;
        }
        
        .response-form h5 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .alert-success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
        }
        
        .alert-error {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
            border: none;
        }
    </style>
</head>
<body>
    <div class="manage-container">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-calendar-check"></i>
                إدارة طلبات الإجازة
            </h1>
        </div>
        
        <?php if($message): ?>
            <div class="alert alert-<?php echo $messageType; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>
        
        <!-- فلترة البيانات -->
        <div class="filter-card">
            <h5><i class="fas fa-filter"></i> فلترة البيانات</h5>
            <form method="GET">
                <div class="filter-row">
                    <div class="form-group">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" name="from_date" class="form-control" value="<?php echo $_GET['from_date'] ?? ''; ?>">
                    </div>
                    <div class="form-group">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" name="to_date" class="form-control" value="<?php echo $_GET['to_date'] ?? ''; ?>">
                    </div>
                    <div class="form-group">
                        <label class="form-label">المستخدم</label>
                        <select name="user_id" class="form-select">
                            <option value="">جميع المستخدمين</option>
                            <?php while($user = $users_result->fetch_assoc()): ?>
                                <option value="<?php echo $user['id_user']; ?>" 
                                    <?php echo (isset($_GET['user_id']) && $_GET['user_id'] == $user['id_user']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($user['user_name']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">نوع الإجازة</label>
                        <select name="leave_type" class="form-select">
                            <option value="">جميع الأنواع</option>
                            <option value="مرضية" <?php echo (isset($_GET['leave_type']) && $_GET['leave_type'] == 'مرضية') ? 'selected' : ''; ?>>مرضية</option>
                            <option value="عرضية" <?php echo (isset($_GET['leave_type']) && $_GET['leave_type'] == 'عرضية') ? 'selected' : ''; ?>>عرضية</option>
                            <option value="طارئة" <?php echo (isset($_GET['leave_type']) && $_GET['leave_type'] == 'طارئة') ? 'selected' : ''; ?>>طارئة</option>
                            <option value="زمنية" <?php echo (isset($_GET['leave_type']) && $_GET['leave_type'] == 'زمنية') ? 'selected' : ''; ?>>زمنية</option>
                            <option value="ظروف أخرى" <?php echo (isset($_GET['leave_type']) && $_GET['leave_type'] == 'ظروف أخرى') ? 'selected' : ''; ?>>ظروف أخرى</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">الحالة</label>
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="قيد المراجعة" <?php echo (isset($_GET['status']) && $_GET['status'] == 'قيد المراجعة') ? 'selected' : ''; ?>>قيد المراجعة</option>
                            <option value="موافق" <?php echo (isset($_GET['status']) && $_GET['status'] == 'موافق') ? 'selected' : ''; ?>>موافق</option>
                            <option value="غير موافق" <?php echo (isset($_GET['status']) && $_GET['status'] == 'غير موافق') ? 'selected' : ''; ?>>غير موافق</option>
                        </select>
                    </div>
                </div>
                <button type="submit" class="filter-btn">
                    <i class="fas fa-search"></i> فلترة
                </button>
                <a href="manage_leaves.php" class="filter-btn" style="background: #6c757d; margin-right: 10px; text-decoration: none;">
                    <i class="fas fa-refresh"></i> إعادة تعيين
                </a>
            </form>
        </div>

        <!-- عرض طلبات الإجازة -->
        <?php if($result->num_rows > 0): ?>
            <?php while($leave = $result->fetch_assoc()): ?>
                <div class="leave-card">
                    <div class="leave-header">
                        <div>
                            <h3 class="employee-name"><?php echo htmlspecialchars($leave['employee_name']); ?></h3>
                            <span class="leave-type-badge"><?php echo $leave['leave_type']; ?></span>
                        </div>
                        <span class="status-badge status-<?php
                            echo $leave['status'] == 'قيد المراجعة' ? 'pending' :
                                ($leave['status'] == 'موافق' ? 'approved' : 'rejected');
                        ?>">
                            <?php echo $leave['status']; ?>
                        </span>
                    </div>

                    <div class="date-range">
                        <i class="fas fa-calendar"></i>
                        من <?php echo date('Y/m/d', strtotime($leave['start_date'])); ?>
                        إلى <?php echo date('Y/m/d', strtotime($leave['end_date'])); ?>
                        (<?php echo $leave['days_count']; ?> يوم)
                    </div>

                    <div class="leave-info">
                        <div class="info-item">
                            <i class="fas fa-user"></i>
                            <span>المستخدم: <?php echo htmlspecialchars($leave['user_name']); ?></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-calendar-plus"></i>
                            <span>تاريخ الطلب: <?php echo date('Y/m/d', strtotime($leave['request_date'])); ?></span>
                        </div>
                        <?php if($leave['response_date']): ?>
                            <div class="info-item">
                                <i class="fas fa-clock"></i>
                                <span>تاريخ الرد: <?php echo date('Y/m/d H:i', strtotime($leave['response_date'])); ?></span>
                            </div>
                        <?php endif; ?>
                        <?php if($leave['response_by_name']): ?>
                            <div class="info-item">
                                <i class="fas fa-user-tie"></i>
                                <span>رد بواسطة: <?php echo htmlspecialchars($leave['response_by_name']); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="leave-details">
                        <strong>تفاصيل الإجازة:</strong><br>
                        <?php echo nl2br(htmlspecialchars($leave['leave_details'])); ?>
                    </div>

                    <?php if($leave['admin_response']): ?>
                        <div class="leave-details" style="background: #e8f5e8; border-right-color: #28a745;">
                            <strong>رد الإدارة:</strong><br>
                            <?php echo nl2br(htmlspecialchars($leave['admin_response'])); ?>
                        </div>
                    <?php endif; ?>

                    <?php if($leave['status'] == 'قيد المراجعة'): ?>
                        <div class="response-form">
                            <h5><i class="fas fa-reply"></i> الرد على طلب الإجازة</h5>
                            <form method="POST">
                                <input type="hidden" name="leave_id" value="<?php echo $leave['id_leave']; ?>">

                                <div class="form-group">
                                    <label class="form-label">حالة الطلب</label>
                                    <select name="status" class="form-select" required>
                                        <option value="">اختر الحالة</option>
                                        <option value="موافق">موافق</option>
                                        <option value="غير موافق">غير موافق</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">ملاحظات الإدارة</label>
                                    <textarea name="admin_response" class="form-control" rows="3"
                                        placeholder="اكتب ملاحظاتك هنا..." required></textarea>
                                </div>

                                <div class="btn-group">
                                    <button type="submit" name="respond_leave" class="btn btn-success">
                                        <i class="fas fa-check"></i> إرسال الرد
                                    </button>
                                </div>
                            </form>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endwhile; ?>
        <?php else: ?>
            <div class="leave-card">
                <div style="text-align: center; padding: 50px; color: #6c757d;">
                    <i class="fas fa-calendar-alt" style="font-size: 64px; margin-bottom: 20px; color: #dee2e6;"></i>
                    <h3>لا توجد طلبات إجازة</h3>
                    <p>لا توجد طلبات إجازة تطابق معايير البحث المحددة</p>
                </div>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
