<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Mod"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>

<?php
$user='kidzrcle_rwda';
$pass='kidzrcle_rwda';
$con= new mysqli("localhost",$user,$pass,'kidzrcle_rwda');
$con->set_charset("utf8");
  

$sql="SELECT * FROM stud_tb WHERE userID={$_SESSION['user']->id_user}";
$getUser=mysqli_query($con,$sql);
$datenow=date('Y-m-d');
$Date=date('Y-m-d', strtotime($datenow. ' + 10 days'));
$Date2=date('Y-m-d', strtotime($datenow. ' + 1 days'));
$Date3=date('Y-m-d', strtotime($datenow. ' + 30 days'));
$Date4=date('Y-m-d', strtotime($datenow. ' + 5 days'));
$Date5=date('Y-m-d', strtotime($datenow. ' + 365 days'));
$sqlA2="SELECT * FROM stud_tb,stud_pay,users_tb WHERE DATE(stud_pay.date_exp) BETWEEN date('$Date2')AND Date('$Date5') AND stud_tb.id=stud_pay.id_stud AND users_tb.id_user=stud_tb.userID AND users_tb.user_name='A2' ";
$resA2=mysqli_query($con,$sqlA2);

$sqlA3="SELECT * FROM stud_tb,stud_pay,users_tb WHERE DATE(stud_pay.date_exp) BETWEEN date('$Date2')AND Date('$Date5') AND stud_tb.id=stud_pay.id_stud AND users_tb.id_user=stud_tb.userID AND users_tb.user_name='A3' ";
$resA3=mysqli_query($con,$sqlA3);

$sqlA4="SELECT * FROM stud_tb,stud_pay,users_tb WHERE DATE(stud_pay.date_exp) BETWEEN date('$Date2')AND Date('$Date5') AND stud_tb.id=stud_pay.id_stud AND users_tb.id_user=stud_tb.userID AND users_tb.user_name='A4' ";
$resA4=mysqli_query($con,$sqlA4);


$sqlA5="SELECT * FROM stud_tb,stud_pay,users_tb WHERE DATE(stud_pay.date_exp) BETWEEN date('$Date2')AND Date('$Date5') AND stud_tb.id=stud_pay.id_stud AND users_tb.id_user=stud_tb.userID AND users_tb.user_name='A5' ";
$resA5=mysqli_query($con,$sqlA5);

$sqlA6="SELECT * FROM stud_tb,stud_pay,users_tb WHERE DATE(stud_pay.date_exp) BETWEEN date('$Date2')AND Date('$Date5') AND stud_tb.id=stud_pay.id_stud AND users_tb.id_user=stud_tb.userID AND users_tb.user_name='A6' ";
$resA6=mysqli_query($con,$sqlA6);

$sqlA7="SELECT * FROM stud_tb,stud_pay,users_tb WHERE DATE(stud_pay.date_exp) BETWEEN date('$Date2')AND Date('$Date5') AND stud_tb.id=stud_pay.id_stud AND users_tb.id_user=stud_tb.userID AND users_tb.user_name='A7' ";
$resA7=mysqli_query($con,$sqlA7);

$sqlA8="SELECT * FROM stud_tb,stud_pay,users_tb WHERE DATE(stud_pay.date_exp) BETWEEN date('$Date2')AND Date('$Date5') AND stud_tb.id=stud_pay.id_stud AND users_tb.id_user=stud_tb.userID AND users_tb.user_name='A8' ";
$resA8=mysqli_query($con,$sqlA8);











?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الرئيسية</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <script src="js/all.js"></script>
    <link rel="icon" href="css/icon.ico">
   
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php" ?>
   </head>
   <body>
       <?php include "../includes/notification_component.php"; ?>
   <div class="rect" id="clock">
        <div class="cir">
            <div class="inner_cir">
                <div class="brand">KIDS ACADMEY</div>
                <div class="center"></div>
                <div id="hr"></div>
                <div id="min"></div>
                <div id="sec"></div>
            </div>
        </div>
    </div>
  <div class="grid">
  
  <div class="col"> 
      
        <a href="A4.php">
        <h3>A4</h3>
        <h4>سما بسماية</h4>
        <?php echo mysqli_num_rows($resA4); ?></a>
  </div>
  <div class="col"> 
      <a href="A3.php">
  <h3>A3</h3>
  <h4>السنابــل</h4>
      <?php echo mysqli_num_rows($resA3); ?></a>
</div>
  <div class="col"> 
      
      <a href="A2.php">
      <h3>A2</h3>
        <h4>رياحين بسماية</h4>
      <?php echo mysqli_num_rows($resA2); ?></a>
</div>

  <div class="col"> 
     
        <a href="A7.php">
        <h3>A7</h3>
        <h4>هيا نبدأ </h4>
        <?php echo mysqli_num_rows($resA7); ?></a>
  </div>
  <div class="col"> 
      
        <a href="A6.php">
        <h3>A6</h3>
        <h4>الروان </h4>
        <?php echo mysqli_num_rows($resA6); ?></a>
  </div>
  <div class="col"> 
      
      <a href="A5.php">
      <h3>A5</h3>
        <h4>أحلام الطفولة</h4>
      <?php echo mysqli_num_rows($resA5); ?></a>
</div>
  <div class="col last">
  <a href="A8.php">
  <h3>A8</h3>
        <h4>مدينة الاحلام </h4>
    <?php echo mysqli_num_rows($resA8); ?>
    </a>
  </div>

  <div class="col">
      <a href="manage_needs.php" id="needs_button" style="position: relative;">
          <h3>الاحتياجات</h3>
          <i class="fas fa-clipboard-list" style="font-size: 2em; color: #667eea;"></i>
      </a>
  </div>

  <div class="col">
      <a href="manage_leaves.php" id="leaves_button" style="position: relative;">
          <h3>طلبات الإجازة</h3>
          <i class="fas fa-calendar-check" style="font-size: 2em; color: #28a745;"></i>
      </a>
  </div>

  </div>
   <footer class=footer_p>
    <p>جميع الحقوق محفوظة لمؤسسة كيدز اكادمي </p>
</footer>
<script >
var sec=0;
var hor=0;
var min=0
var d=new Date();

setInterval(
    function(){
        d=new Date();
        sec= d.getSeconds() *6 ;
        min= d.getMinutes() * 6 ;
        hor=d.getHours() *30 + Math.round(min/12);
        document.getElementById("sec").style.transform="rotate("+sec+"deg)";
        document.getElementById("min").style.transform="rotate("+min+"deg)";
        document.getElementById("hr").style.transform="rotate("+hor+"deg)";
    },1000
);
function isChecked(){
    if(document.getElementById("click").checked

    ){
        document.getElementById("clock").style.display="flex";
    }else{
        document.getElementById("clock").style.display="none";
    }
}

</script>
</html>
