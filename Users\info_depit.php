<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="User"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}
 
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المصروفات  </title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
    <script src="js/jquery.min.js"></script>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php";
      include "addon/dbcon.php"?>
   </head>
    <body>
       <div class="search">
       <input type="text" id="live_search" placeholder="يمكنك البحث عن معلومات المصروف هنا">
  <button class="btn btn-success text-light" name="sub"  ><a href="../Admin/addon/export_depit.php">تحميل اكسل</a> </button>
  <button class="btn btn-secondary text-light" name="finde"  ><a href="add_depit.php"> اضافة مصروف</a> </button>
  
    
    
    </div>
    <table class="table" id="tb" >
  <thead>
    <tr>
    <th scope="col">العمليات</th>
      <th scope="col">المستخدم</th>
      <th scope="col"> تاريخ عملية المصروفات</th>
      <th scope="col">قيمة المصروفات</th>
      <th scope="col">وصف المصروفات</th>
      
    </tr>
  </thead>
  <tbody id="myTable" >
 
  </tbody>
</table>
<script>

  var selc =<?php echo $_SESSION['user']->id_user?>;
        $.ajax({
          method: "POST",
          url: "addon/info_depitF.php",
          data: {id:selc},
          success: function (data) {
            $("#myTable").html(data);
          }
        
        })

      
      
</script>
<section id="deletmodle">
      <div class="modal-box">
        <h2>! تنبيه </h2>
        <h3>هل انته متأكد من عملية الحذف كون البيانات لايمكن استرجاعها</h3>

        <div class="buttons-method">
          <button class="close-btn">اغلاق</button>
         <button name="remov" id="rm" class="remove-btn">حذف</button>
        </div>
      </div>
    </section>

</body>
    <script>
        function deletdata(id){
        $("#deletmodle").addClass("active");
        console.log(id)
        $(".table").addClass("active");
        $(".close-btn").click(function () { 
          $(".table").removeClass("active")
          $("#deletmodle").removeClass("active")
        });
        $("#rm").click(function () { 
          $(".table").removeClass("active")
           console.log(id)
          $.ajax({url:'addon/removedepit.php',
          method:"POST",
          data:({name:id}),
          success:function(response){
          $("#deletmodle").removeClass("active");
          jQuery("#tr_"+id).css("background","#eaeaea");
          jQuery("#tr_"+id).hide(2000);
          
          
        

      }
    });
        });
      }
    
    </script>
       <script>
      $(document).ready(function () {
        $("#live_search").keyup(function(){
          var input = $(this).val();
          
          if(input != ""){
            $.ajax({
              method: "POST",
              url: "addon/searchFD.php",
              data: {input:input},
              success:function (data) {
                
                $("#tb").html(data);
              }
            });

          }else{
            location.reload(true)
          }
         
        })
      });
    </script>
    
</body>


</html>