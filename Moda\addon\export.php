<?php
// تحسين الاتصال بقاعدة البيانات
$user = "irjnpfzw_seana";
$pass = "irjnpfzw_seana";
$host = "localhost";
$database = "irjnpfzw_seana";

$con = new mysqli($host, $user, $pass, $database);

// التحقق من الاتصال
if ($con->connect_error) {
    die("فشل الاتصال بقاعدة البيانات: " . $con->connect_error);
}

// تحديد ترميز UTF-8
$con->set_charset("utf8");

// التحقق من وجود المعاملات
if (!isset($_GET['useratt']) || !isset($_GET['dateatt'])) {
    die("معاملات مفقودة");
}

$user2 = $_GET['useratt'];
$date2 = $_GET['dateatt'];

// استعلام محسن مع معالجة الأخطاء وإضافة حالة الحضور
$sql = "SELECT users_tb.user_name, stud_tb.name, stud_tb.p_name, stud_tb.catg,
                stat.data_stat, stat.stat_stud, stud_pay.date_exp, stud_pay.cash_stud
        FROM users_tb
        INNER JOIN stud_tb ON users_tb.id_user = stud_tb.userID
        INNER JOIN stat ON stud_tb.id = stat.id_stud
        LEFT JOIN stud_pay ON stud_tb.id = stud_pay.id_stud
        WHERE users_tb.id_user = ? AND stat.data_stat = ?";

// استخدام prepared statements لمنع SQL injection
$stmt = $con->prepare($sql);
if (!$stmt) {
    die("خطأ في تحضير الاستعلام: " . $con->error);
}

$stmt->bind_param("is", $user2, $date2);
$stmt->execute();
$result = $stmt->get_result();

if (!$result) {
    die("خطأ في تنفيذ الاستعلام: " . $con->error);
}

$fileName = "حضور_الطلاب_" . date("Y-m-d") . ".csv";

// تحديد headers للتحميل
header("Content-Type: text/csv; charset=utf-8");
header("Content-Disposition: attachment; filename=\"$fileName\"");
header("Pragma: no-cache");
header("Expires: 0");

// إنشاء output stream
$output = fopen('php://output', 'w');

// إضافة BOM للدعم UTF-8 في Excel
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// كتابة رؤوس الأعمدة مع إضافة عمود حالة الحضور
$headers = ['اسم الطالب', 'صنف الدراسة', 'تاريخ الحضور', 'حالة الحضور', 'اسم المستخدم', 'حالة الاشتراك'];
fputcsv($output, $headers);

// التحقق من وجود نتائج
if ($result->num_rows == 0) {
    fputcsv($output, ['لا توجد بيانات للتاريخ والمستخدم المحددين', '', '', '', '', '']);
} else {
    // جلب البيانات
    while ($row = $result->fetch_assoc()) {
        // تحديد حالة الاشتراك
        $subscription_status = 'غير محدد';
        if (!empty($row['date_exp'])) {
            $expiry_date = new DateTime($row['date_exp']);
            $current_date = new DateTime();
            
            if ($expiry_date > $current_date) {
                $subscription_status = 'نشط';
            } else {
                $subscription_status = 'منتهي';
            }
        }
        
        // تحديد حالة الحضور
        $attendance_status = $row['stat_stud'] ?? 'غير محدد';
        
        $rowData = [
            $row['name'] ?? 'N/A',
            $row['catg'] ?? 'N/A',
            $row['data_stat'] ?? 'N/A',
            $attendance_status,
            $row['user_name'] ?? 'N/A',
            $subscription_status
        ];
        
        fputcsv($output, $rowData);
    }
}

fclose($output);
$stmt->close();
$con->close();
exit();
?>