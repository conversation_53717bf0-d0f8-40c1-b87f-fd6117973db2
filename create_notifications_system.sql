-- إن<PERSON><PERSON>ء جدول الإشعارات
CREATE TABLE IF NOT EXISTS `notifications` (
  `id_notification` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `type` enum('need_request','leave_request','need_response','leave_response') NOT NULL,
  `related_id` int(11) NOT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_notification`),
  KEY `user_id` (`user_id`),
  KEY `is_read` (`is_read`),
  KEY `type` (`type`),
  KEY `created_at` (`created_at`),
  CONSTRAINT `notifications_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users_tb` (`id_user`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة triggers لإنشاء الإشعارات تلقائياً

-- Trigger عند إضافة طلب احتياج جديد
DELIMITER $$
CREATE TRIGGER `after_need_insert` AFTER INSERT ON `needs_requests`
FOR EACH ROW
BEGIN
    -- إشعار للإدارة والمدققين
    INSERT INTO notifications (user_id, title, message, type, related_id)
    SELECT u.id_user, 
           'طلب احتياج جديد',
           CONCAT('تم تقديم طلب احتياج جديد: ', NEW.need_name, ' من المستخدم: ', (SELECT user_name FROM users_tb WHERE id_user = NEW.user_id)),
           'need_request',
           NEW.id_need
    FROM users_tb u 
    WHERE u.role IN ('Admin', 'Mod');
END$$

-- Trigger عند إضافة طلب إجازة جديد
CREATE TRIGGER `after_leave_insert` AFTER INSERT ON `leave_requests`
FOR EACH ROW
BEGIN
    -- إشعار للإدارة والمدققين
    INSERT INTO notifications (user_id, title, message, type, related_id)
    SELECT u.id_user, 
           'طلب إجازة جديد',
           CONCAT('تم تقديم طلب إجازة جديد من: ', NEW.employee_name, ' للمستخدم: ', (SELECT user_name FROM users_tb WHERE id_user = NEW.user_id)),
           'leave_request',
           NEW.id_leave
    FROM users_tb u 
    WHERE u.role IN ('Admin', 'Mod');
END$$

-- Trigger عند تحديث حالة طلب الاحتياج
CREATE TRIGGER `after_need_update` AFTER UPDATE ON `needs_requests`
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status AND NEW.status != 'قيد المراجعة' THEN
        -- إشعار للمستخدم صاحب الطلب
        INSERT INTO notifications (user_id, title, message, type, related_id)
        VALUES (NEW.user_id, 
                'تم الرد على طلب الاحتياج',
                CONCAT('تم تحديث حالة طلب الاحتياج: ', NEW.need_name, ' إلى: ', NEW.status),
                'need_response',
                NEW.id_need);
    END IF;
END$$

-- Trigger عند تحديث حالة طلب الإجازة
CREATE TRIGGER `after_leave_update` AFTER UPDATE ON `leave_requests`
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status AND NEW.status != 'قيد المراجعة' THEN
        -- إشعار للمستخدم صاحب الطلب
        INSERT INTO notifications (user_id, title, message, type, related_id)
        VALUES (NEW.user_id, 
                'تم الرد على طلب الإجازة',
                CONCAT('تم تحديث حالة طلب الإجازة للموظف: ', NEW.employee_name, ' إلى: ', NEW.status),
                'leave_response',
                NEW.id_leave);
    END IF;
END$$

DELIMITER ;

-- إضافة فهارس لتحسين الأداء
CREATE INDEX idx_notifications_user_read ON notifications(user_id, is_read);
CREATE INDEX idx_notifications_type_read ON notifications(type, is_read);
CREATE INDEX idx_notifications_created ON notifications(created_at DESC);
