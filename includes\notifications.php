<?php
// ملف إدارة الإشعارات

// دالة للحصول على عدد الإشعارات غير المقروءة
function getUnreadNotificationsCount($con, $user_id) {
    $stmt = $con->prepare("SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    return $row['count'];
}

// دالة للحصول على عدد طلبات الاحتياجات الجديدة للإدارة
function getNewNeedsCount($con, $user_role) {
    if($user_role == 'Admin' || $user_role == 'Mod') {
        $stmt = $con->prepare("SELECT COUNT(*) as count FROM needs_requests WHERE status = 'قيد المراجعة'");
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        return $row['count'];
    }
    return 0;
}

// دالة للحصول على عدد طلبات الإجازة الجديدة للإدارة
function getNewLeavesCount($con, $user_role) {
    if($user_role == 'Admin' || $user_role == 'Mod') {
        $stmt = $con->prepare("SELECT COUNT(*) as count FROM leave_requests WHERE status = 'قيد المراجعة'");
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        return $row['count'];
    }
    return 0;
}

// دالة للحصول على الإشعارات الأخيرة
function getRecentNotifications($con, $user_id, $limit = 10) {
    $stmt = $con->prepare("SELECT * FROM notifications WHERE user_id = ? ORDER BY created_at DESC LIMIT ?");
    $stmt->bind_param("ii", $user_id, $limit);
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->fetch_all(MYSQLI_ASSOC);
}

// دالة لتمييز الإشعار كمقروء
function markNotificationAsRead($con, $notification_id, $user_id) {
    $stmt = $con->prepare("UPDATE notifications SET is_read = 1 WHERE id_notification = ? AND user_id = ?");
    $stmt->bind_param("ii", $notification_id, $user_id);
    return $stmt->execute();
}

// دالة لتمييز جميع الإشعارات كمقروءة
function markAllNotificationsAsRead($con, $user_id) {
    $stmt = $con->prepare("UPDATE notifications SET is_read = 1 WHERE user_id = ? AND is_read = 0");
    $stmt->bind_param("i", $user_id);
    return $stmt->execute();
}

// دالة لحذف الإشعارات القديمة (أكثر من 30 يوم)
function cleanOldNotifications($con) {
    $stmt = $con->prepare("DELETE FROM notifications WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)");
    return $stmt->execute();
}

// دالة لإنشاء إشعار يدوي
function createNotification($con, $user_id, $title, $message, $type, $related_id) {
    $stmt = $con->prepare("INSERT INTO notifications (user_id, title, message, type, related_id) VALUES (?, ?, ?, ?, ?)");
    $stmt->bind_param("isssi", $user_id, $title, $message, $type, $related_id);
    return $stmt->execute();
}

// دالة للحصول على أيقونة الإشعار حسب النوع
function getNotificationIcon($type) {
    switch($type) {
        case 'need_request':
            return 'fas fa-clipboard-list';
        case 'leave_request':
            return 'fas fa-calendar-check';
        case 'need_response':
            return 'fas fa-reply';
        case 'leave_response':
            return 'fas fa-calendar-alt';
        default:
            return 'fas fa-bell';
    }
}

// دالة للحصول على لون الإشعار حسب النوع
function getNotificationColor($type) {
    switch($type) {
        case 'need_request':
            return '#667eea';
        case 'leave_request':
            return '#28a745';
        case 'need_response':
            return '#17a2b8';
        case 'leave_response':
            return '#ffc107';
        default:
            return '#6c757d';
    }
}

// دالة لتنسيق وقت الإشعار
function formatNotificationTime($datetime) {
    $time = strtotime($datetime);
    $now = time();
    $diff = $now - $time;
    
    if ($diff < 60) {
        return 'الآن';
    } elseif ($diff < 3600) {
        $minutes = floor($diff / 60);
        return $minutes . ' دقيقة';
    } elseif ($diff < 86400) {
        $hours = floor($diff / 3600);
        return $hours . ' ساعة';
    } elseif ($diff < 604800) {
        $days = floor($diff / 86400);
        return $days . ' يوم';
    } else {
        return date('Y/m/d', $time);
    }
}

// دالة لعرض شارة العدد
function displayCountBadge($count, $color = '#dc3545') {
    if ($count > 0) {
        $displayCount = $count > 99 ? '99+' : $count;
        return "<span class='notification-badge' style='background: $color;'>$displayCount</span>";
    }
    return '';
}

// CSS للشارات والإشعارات
function getNotificationCSS() {
    return "
    <style>
        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: bold;
            min-width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            animation: pulse 2s infinite;
        }
        
        .notification-container {
            position: relative;
            display: inline-block;
        }
        
        .notification-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            min-width: 300px;
            max-width: 400px;
            z-index: 1000;
            display: none;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .notification-item {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .notification-item:hover {
            background: #f8f9fa;
        }
        
        .notification-item.unread {
            background: #e3f2fd;
            border-right: 4px solid #2196f3;
        }
        
        .notification-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 5px;
        }
        
        .notification-title {
            font-weight: bold;
            color: #2c3e50;
            font-size: 14px;
        }
        
        .notification-time {
            color: #6c757d;
            font-size: 12px;
            margin-right: auto;
        }
        
        .notification-message {
            color: #495057;
            font-size: 13px;
            line-height: 1.4;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .notification-bell {
            position: relative;
            cursor: pointer;
            padding: 10px;
            border-radius: 50%;
            transition: background 0.3s ease;
        }
        
        .notification-bell:hover {
            background: rgba(255,255,255,0.1);
        }
    </style>
    ";
}
?>
